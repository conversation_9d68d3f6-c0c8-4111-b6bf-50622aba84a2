import { prop } from '~/assets/libs/nosqlite/decorator';

class UserInfo {
  @prop({ type: String })
  firstName!: string;

  @prop({ type: String })
  lastName!: string;

  @prop({ type: Boolean })
  gender!: boolean;
}

export class UserModel {
  @prop({ type: Boolean })
  isDeleted!: boolean;

  @prop({ type: String, index: true })
  name!: string;

  @prop({ type: String, index: true })
  email!: string;

  @prop({ type: String })
  password!: string;

  @prop({ type: UserInfo })
  info?: UserInfo;

  @prop({ type: Date })
  createdAt?: Date;

  @prop({ type: Date })
  updatedAt?: Date;
}
