{"name": "spendwise", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint"}, "dependencies": {"@capacitor-community/sqlite": "^7.0.1", "@capacitor/android": "^7.4.2", "@capacitor/app": "7.0.1", "@capacitor/core": "7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.2", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@capawesome-team/capacitor-file-opener": "^7.0.1", "@capawesome/capacitor-file-picker": "^7.2.0", "@capgo/capacitor-social-login": "^7.8.2", "@types/react-router": "^5.1.20", "capacitor-secure-storage-plugin": "^0.11.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "i18next": "^25.3.2", "jeep-sqlite": "^2.8.0", "lodash": "^4.17.21", "motion": "^12.23.6", "pako": "^2.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^15.6.0", "react-router": "^7.6.3", "react-router-dom": "^7.6.3", "reflect-metadata": "^0.2.2"}, "devDependencies": {"@capacitor/cli": "7.4.2", "@tailwindcss/vite": "^4.1.11", "@types/lodash": "^4.17.20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "^4.6.0", "daisyui": "^5.0.46", "dotenv": "^17.2.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vite": "~7.0.4", "vite-plugin-static-copy": "^3.1.1"}, "overrides": {"rollup": "4.44.0"}}