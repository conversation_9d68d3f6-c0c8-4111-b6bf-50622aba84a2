import { BrowserRouter, Outlet, Route, Routes } from 'react-router-dom';

import TestPage from './pages/TestPage';

function Layout() {
  return (
    <div className="flex flex-col h-screen">
      <main className="flex-1 overflow-auto no-scrollbar">
        <Outlet />
      </main>
    </div>
  );
}

export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<TestPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
