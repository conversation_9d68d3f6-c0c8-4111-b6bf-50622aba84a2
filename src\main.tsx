import 'reflect-metadata';

import dayjs from 'dayjs';
import React from 'react';
import { createRoot } from 'react-dom/client';

import App from './App';
import { NoSqliteInit, NoSqliteModel, query } from './assets/libs/nosqlite/index';
import { database } from './configs/database';
import { CommentModel } from './models/commentModel';
import { UserModel } from './models/userModel';

const container = document.getElementById('root');
const root = createRoot(container!);

(async () => {
  const db = await database();
  await NoSqliteInit([UserModel, CommentModel], db);

  const user: UserModel = {
    isDeleted: false,
    name: 'anle',
    email: '<EMAIL>',
    password: '123',
    info: {
      firstName: 'an',
      lastName: 'le',
      gender: true,
    },
    createdAt: dayjs().toDate(),
    updatedAt: dayjs().toDate(),
  };

  const comment: CommentModel = {
    isDeleted: false,
    user: '687b065b9f58d0d33a4aec0d',
    content: 'Hello world',
    createdAt: dayjs().toDate(),
    updatedAt: dayjs().toDate(),
  };

  const userModel = new NoSqliteModel(UserModel);
  const commentModel = new NoSqliteModel(CommentModel);

  // await commentModel.insertOne(comment);

  // await userModel.insertOne(user);
  // await userModel.insertMany([user, user, user]);

  console.log(
    await userModel.updateOne(
      {},
      {
        isDeleted: true,
      },
    ),
  );

  // const selectQuery = `SELECT * FROM sqlite_master WHERE name='user'`;

  // await db.execute(insertQuery);
  // const ret = await db.query(selectQuery);
  // console.log(ret.values);

  // console.log('Main');

  (window as any).query = query;
})();

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);
