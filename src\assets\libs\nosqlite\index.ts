import 'reflect-metadata';

import { QueryBuilder } from './builder';
import { getColumnType, uniqueId } from './helper';
import { Filter } from './type';

let dbInstance: any;

export async function NoSqliteInit(models: any[], database: any) {
  dbInstance = database;

  for (const model of models) {
    const table = model.name.toLowerCase().replace('model', '');

    await database.execute(`
        CREATE TABLE IF NOT EXISTS ${table} (
          _id TEXT PRIMARY KEY NOT NULL,
          data TEXT
        )`);

    const props = Reflect.getMetadata('props', model) || {};

    const handleProps = async (props: Record<string, any>, prefix = '', jsonPath = '$') => {
      for (const [key, config] of Object.entries(props)) {
        const colName = prefix ? `${prefix}_${key}` : key;
        const path = `${jsonPath}.${key}`;

        const isNested = typeof config?.type === 'function' && Reflect.getMetadata('props', config.type);

        if (isNested) {
          const nestedProps = Reflect.getMetadata('props', config.type);
          await handleProps(nestedProps, colName, path);
          continue;
        }

        const colType = getColumnType(config?.type);
        const columnSQL = `
            ALTER TABLE ${table} ADD COLUMN ${colName} ${colType}
            GENERATED ALWAYS AS (json_extract(data, '${path}')) STORED
          `;
        try {
          await database.execute(columnSQL);
        } catch (e: any) {
          if (!e.message.includes('duplicate column')) {
            console.log(`Failed to add column ${colName} to ${table}:`, e.message);
          }
        }

        if (config?.index) {
          const indexName = `idx_${table}_${colName}`;
          const indexSQL = `CREATE INDEX IF NOT EXISTS ${indexName} ON ${table}(${colName})`;
          await database.execute(indexSQL);
        }
      }
    };

    await handleProps(props);

    console.log(`${table} created`);
  }
}

export async function query(sql: string, params: any[]) {
  if (!dbInstance) throw new Error('Database not initialized');

  if (sql.trim().toLowerCase().startsWith('select') || sql.trim().toLowerCase().startsWith('pragma'))
    return (await dbInstance.query(sql, params)).values;
  else return (await dbInstance.run(sql, params)).changes;
}

export class NoSqliteModel<T> {
  private table: string;
  private model: any;
  private query: any;

  constructor(model: new () => T) {
    this.model = model;
    this.query = query;
    this.table = model.name.toLowerCase().replace('model', '');
  }

  find(filter: Filter<T> = {}) {
    return new QueryBuilder<T>(this.table, this.query, this.model).where(filter);
  }

  findOne(filter: Filter<T> = {}) {
    return this.find(filter).first();
  }

  findById(id: string) {
    return this.findOne({ _id: id } as Partial<Record<keyof T, any>>);
  }

  async insertOne(data: Partial<T & { _id?: string }>) {
    const _id = data._id ?? uniqueId();
    const fullItem = { _id, ...data };
    const json = JSON.stringify(fullItem);
    await this.query(`INSERT INTO ${this.table} (_id, data) VALUES (?, ?)`, [_id, json]);
    return fullItem;
  }

  async insertMany(data: Partial<T & { _id?: string }>[]) {
    if (!Array.isArray(data)) throw new Error('data must be an array');
    if (data.length === 0) return [];

    const placeholders: string[] = [];
    const values: any[] = [];
    const result: (T & { _id: string })[] = [];

    for (const item of data) {
      const _id = item._id ?? uniqueId();
      const fullItem = { ...item, _id } as T & { _id: string };
      const json = JSON.stringify(fullItem);

      placeholders.push('(?, ?)');
      values.push(_id, json);
      result.push(fullItem);
    }

    const sql = `INSERT INTO ${this.table} (_id, data) VALUES ${placeholders.join(', ')}`;
    await this.query(sql, values);

    return result;
  }

  async updateOne(filter: Filter<T>, data: Partial<T>) {
    const record = await this.findOne(filter);
    if (!record) return null;

    const { _id } = record as T & { _id: string };
    const updated = { ...record, ...data, _id };
    const json = JSON.stringify(updated);
    await this.query(`UPDATE ${this.table} SET data = ? WHERE _id = ?`, [json, _id]);
    return updated;
  }

  async updateMany(filter: Filter<T>, data: Partial<T>) {
    const records = await this.find(filter);
    if (!records.length) return [];

    const updatedDocs: (T & { _id: string })[] = [];
    for (const record of records) {
      const { _id } = record as T & { _id: string };
      const updated = { ...record, ...data, _id };
      const json = JSON.stringify(updated);
      await this.query(`UPDATE ${this.table} SET data = ? WHERE _id = ?`, [json, _id]);
      updatedDocs.push(updated);
    }

    return updatedDocs;
  }

  async deleteOne(filter: Filter<T>) {
    const record = await this.findOne(filter);
    if (!record) return null;

    const { _id } = record as T & { _id: string };
    await this.query(`DELETE FROM ${this.table} WHERE _id = ?`, [_id]);
    return record;
  }

  async deleteMany(filter: Filter<T>) {
    const records = await this.find(filter);
    if (!records.length) return [];

    const ids = records.map((r) => (r as T & { _id: string })._id);
    const placeholders = ids.map(() => '?').join(', ');
    await this.query(`DELETE FROM ${this.table} WHERE _id IN (${placeholders})`, ids);
    return records;
  }
}
